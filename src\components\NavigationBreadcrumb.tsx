import React from 'react';
import { 
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronRight, 
  Home, 
  ArrowLeft,
  Clock,
  Star,
  Share2
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

interface NavigationBreadcrumbProps {
  items: BreadcrumbItem[];
  currentPage: string;
  showBackButton?: boolean;
  showActions?: boolean;
  className?: string;
}

const NavigationBreadcrumb: React.FC<NavigationBreadcrumbProps> = ({
  items,
  currentPage,
  showBackButton = true,
  showActions = true,
  className
}) => {
  const handleBack = () => {
    window.history.back();
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: currentPage,
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const handleBookmark = () => {
    // Add to favorites/bookmarks logic
    console.log('Bookmarked:', currentPage);
  };

  return (
    <div className={cn(
      "flex items-center justify-between py-4 px-6 bg-white border-b border-gray-200",
      className
    )}>
      <div className="flex items-center space-x-4">
        {/* Back Button */}
        {showBackButton && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBack}
            className="text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        )}

        {/* Breadcrumb Navigation */}
        <Breadcrumb>
          <BreadcrumbList>
            {/* Home */}
            <BreadcrumbItem>
              <BreadcrumbLink href="/" className="flex items-center">
                <Home className="h-4 w-4 mr-1" />
                Dashboard
              </BreadcrumbLink>
            </BreadcrumbItem>

            {/* Dynamic Items */}
            {items.map((item, index) => (
              <React.Fragment key={index}>
                <BreadcrumbSeparator>
                  <ChevronRight className="h-4 w-4" />
                </BreadcrumbSeparator>
                <BreadcrumbItem>
                  {item.href ? (
                    <BreadcrumbLink href={item.href} className="flex items-center">
                      {item.icon && <item.icon className="h-4 w-4 mr-1" />}
                      {item.label}
                    </BreadcrumbLink>
                  ) : (
                    <span className="flex items-center text-gray-600">
                      {item.icon && <item.icon className="h-4 w-4 mr-1" />}
                      {item.label}
                    </span>
                  )}
                </BreadcrumbItem>
              </React.Fragment>
            ))}

            {/* Current Page */}
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbPage className="font-medium text-gray-900">
                {currentPage}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Status Badge */}
        <Badge variant="success" className="ml-2">
          Active
        </Badge>
      </div>

      {/* Action Buttons */}
      {showActions && (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBookmark}
            className="text-gray-600 hover:text-yellow-600"
          >
            <Star className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleShare}
            className="text-gray-600 hover:text-blue-600"
          >
            <Share2 className="h-4 w-4" />
          </Button>

          <div className="flex items-center text-xs text-gray-500 ml-4">
            <Clock className="h-3 w-3 mr-1" />
            Last updated: {new Date().toLocaleDateString()}
          </div>
        </div>
      )}
    </div>
  );
};

export default NavigationBreadcrumb;

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { User as SupabaseUser } from '@supabase/supabase-js';

// Enhanced User interface that matches the expected format
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  role: 'user' | 'admin' | 'premium';
  created_at: string;
  last_sign_in_at?: string;
  email_confirmed_at?: string;
  subscription_status?: 'free' | 'premium' | 'enterprise';
  usage_limits: {
    daily_scrapes: number;
    monthly_scrapes: number;
    max_urls_per_batch: number;
    data_retention_days: number;
  };
}

export interface AuthState {
  user: User | null;
  session: any | null;
  loading: boolean;
  error: string | null;
}

export interface SignUpData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface UpdateProfileData {
  name?: string;
  email?: string;
  avatar_url?: string;
}

interface AuthContextType extends AuthState {
  signUp: (data: SignUpData) => Promise<{ user: User | null; error: string | null }>;
  signIn: (data: SignInData) => Promise<{ user: User | null; error: string | null }>;
  signOut: () => Promise<{ error: string | null }>;
  resetPassword: (email: string) => Promise<{ error: string | null }>;
  updatePassword: (newPassword: string) => Promise<{ error: string | null }>;
  updateProfile: (data: UpdateProfileData) => Promise<{ user: User | null; error: string | null }>;
  refreshUser: () => Promise<void>;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isPremium: boolean;
  canPerformAction: (action: string) => boolean;
  getRemainingUsage: () => {
    dailyScrapesRemaining: number;
    monthlyScrapesRemaining: number;
    canScrapeUrls: (count: number) => boolean;
  };
  // Two-Factor Authentication methods (stubs for now)
  isTwoFactorEnabled: (userId: string) => Promise<boolean>;
  setupTwoFactor: (userId: string, email: string) => Promise<{ setup: any; error: string | null }>;
  enableTwoFactor: (userId: string, token: string) => Promise<{ success: boolean; error: string | null }>;
  verifyTwoFactor: (userId: string, token: string, backupCode?: string) => Promise<{ success: boolean; error: string | null }>;
  disableTwoFactor: (userId: string, token: string) => Promise<{ success: boolean; error: string | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Hook to use authentication context
 */
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

/**
 * Transform Supabase user to our User interface
 */
function transformSupabaseUser(supabaseUser: SupabaseUser): User {
  const firstName = supabaseUser.user_metadata?.first_name || '';
  const lastName = supabaseUser.user_metadata?.last_name || '';
  const name = firstName && lastName ? `${firstName} ${lastName}`.trim() : 
               firstName || lastName || 
               supabaseUser.email?.split('@')[0] || 'User';

  return {
    id: supabaseUser.id,
    email: supabaseUser.email || '',
    name,
    avatar_url: supabaseUser.user_metadata?.avatar_url || '',
    role: 'user', // Default role, can be enhanced with database lookup
    created_at: supabaseUser.created_at,
    last_sign_in_at: supabaseUser.last_sign_in_at,
    email_confirmed_at: supabaseUser.email_confirmed_at,
    subscription_status: 'free', // Default, can be enhanced with database lookup
    usage_limits: {
      daily_scrapes: 100,
      monthly_scrapes: 1000,
      max_urls_per_batch: 10,
      data_retention_days: 30
    }
  };
}

/**
 * Authentication Provider component
 */
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    error: null
  });

  const [usageStats, setUsageStats] = useState({
    dailyScrapesUsed: 0,
    monthlyScrapesUsed: 0,
    lastResetDate: new Date().toDateString()
  });

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        setAuthState({
          user: session?.user ? transformSupabaseUser(session.user) : null,
          session,
          loading: false,
          error: null
        });

        if (session?.user) {
          await loadUsageStats(session.user.id);
        }
      } catch (error) {
        setAuthState({
          user: null,
          session: null,
          loading: false,
          error: error instanceof Error ? error.message : 'Failed to initialize auth'
        });
      }
    };

    initializeAuth();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setAuthState(prev => ({
          ...prev,
          user: session?.user ? transformSupabaseUser(session.user) : null,
          session,
          loading: false,
          error: null
        }));

        if (session?.user) {
          await loadUsageStats(session.user.id);
        } else {
          resetUsageStats();
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const loadUsageStats = async (userId: string) => {
    try {
      const stored = localStorage.getItem(`usage_stats_${userId}`);
      if (stored) {
        const stats = JSON.parse(stored);
        const today = new Date().toDateString();
        
        if (stats.lastResetDate !== today) {
          stats.dailyScrapesUsed = 0;
          stats.lastResetDate = today;
          localStorage.setItem(`usage_stats_${userId}`, JSON.stringify(stats));
        }
        
        setUsageStats(stats);
      } else {
        const initialStats = {
          dailyScrapesUsed: 0,
          monthlyScrapesUsed: 0,
          lastResetDate: new Date().toDateString()
        };
        setUsageStats(initialStats);
        localStorage.setItem(`usage_stats_${userId}`, JSON.stringify(initialStats));
      }
    } catch (error) {
      console.error('Error loading usage stats:', error);
    }
  };

  const resetUsageStats = () => {
    setUsageStats({
      dailyScrapesUsed: 0,
      monthlyScrapesUsed: 0,
      lastResetDate: new Date().toDateString()
    });
  };

  // Auth actions
  const signUp = useCallback(async (data: SignUpData) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const { data: result, error } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            first_name: data.firstName || '',
            last_name: data.lastName || ''
          }
        }
      });

      if (error) {
        setAuthState(prev => ({ ...prev, loading: false, error: error.message }));
        return { user: null, error: error.message };
      }

      return { 
        user: result.user ? transformSupabaseUser(result.user) : null, 
        error: null 
      };
    } catch (error: any) {
      const errorMessage = error.message || 'Sign up failed';
      setAuthState(prev => ({ ...prev, loading: false, error: errorMessage }));
      return { user: null, error: errorMessage };
    }
  }, []);

  const signIn = useCallback(async (data: SignInData) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const { data: result, error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password
      });

      if (error) {
        setAuthState(prev => ({ ...prev, loading: false, error: error.message }));
        return { user: null, error: error.message };
      }

      return { 
        user: result.user ? transformSupabaseUser(result.user) : null, 
        error: null 
      };
    } catch (error: any) {
      const errorMessage = error.message || 'Sign in failed';
      setAuthState(prev => ({ ...prev, loading: false, error: errorMessage }));
      return { user: null, error: errorMessage };
    }
  }, []);

  const signOut = useCallback(async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        return { error: error.message };
      }
      resetUsageStats();
      return { error: null };
    } catch (error: any) {
      return { error: error.message || 'Sign out failed' };
    }
  }, []);

  const resetPassword = useCallback(async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });
      return { error: error?.message || null };
    } catch (error: any) {
      return { error: error.message || 'Password reset failed' };
    }
  }, []);

  const updatePassword = useCallback(async (newPassword: string) => {
    try {
      const { error } = await supabase.auth.updateUser({ password: newPassword });
      return { error: error?.message || null };
    } catch (error: any) {
      return { error: error.message || 'Password update failed' };
    }
  }, []);

  const updateProfile = useCallback(async (data: UpdateProfileData) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const updates: any = {};

      if (data.name) {
        const nameParts = data.name.split(' ');
        updates.data = {
          first_name: nameParts[0] || '',
          last_name: nameParts.slice(1).join(' ') || ''
        };
      }

      if (data.email) {
        updates.email = data.email;
      }

      if (data.avatar_url) {
        updates.data = { ...updates.data, avatar_url: data.avatar_url };
      }

      const { data: result, error } = await supabase.auth.updateUser(updates);

      if (error) {
        setAuthState(prev => ({ ...prev, loading: false, error: error.message }));
        return { user: null, error: error.message };
      }

      const updatedUser = result.user ? transformSupabaseUser(result.user) : null;
      setAuthState(prev => ({ ...prev, user: updatedUser, loading: false }));

      return { user: updatedUser, error: null };
    } catch (error: any) {
      const errorMessage = error.message || 'Profile update failed';
      setAuthState(prev => ({ ...prev, loading: false, error: errorMessage }));
      return { user: null, error: errorMessage };
    }
  }, []);

  const refreshUser = useCallback(async () => {
    if (!authState.user) return;

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setAuthState(prev => ({ ...prev, user: transformSupabaseUser(user) }));
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
    }
  }, [authState.user]);

  // Computed properties
  const isAuthenticated = !!authState.user;
  const isAdmin = authState.user?.role === 'admin';
  const isPremium = authState.user?.subscription_status === 'premium' ||
                   authState.user?.subscription_status === 'enterprise';

  const canPerformAction = useCallback((action: string): boolean => {
    if (!authState.user) return false;

    const permissions = {
      'scrape_urls': true,
      'export_data': true,
      'view_analytics': true,
      'use_marketing_tools': true,
      'bulk_operations': isPremium,
      'advanced_analytics': isPremium,
      'api_access': isPremium,
      'priority_support': isPremium,
      'admin_panel': isAdmin,
      'user_management': isAdmin,
      'system_settings': isAdmin
    };

    return permissions[action as keyof typeof permissions] || false;
  }, [authState.user, isPremium, isAdmin]);

  const getRemainingUsage = useCallback(() => {
    if (!authState.user) {
      return {
        dailyScrapesRemaining: 0,
        monthlyScrapesRemaining: 0,
        canScrapeUrls: () => false
      };
    }

    const { usage_limits } = authState.user;
    const dailyRemaining = Math.max(0, usage_limits.daily_scrapes - usageStats.dailyScrapesUsed);
    const monthlyRemaining = Math.max(0, usage_limits.monthly_scrapes - usageStats.monthlyScrapesUsed);

    return {
      dailyScrapesRemaining: dailyRemaining,
      monthlyScrapesRemaining: monthlyRemaining,
      canScrapeUrls: (count: number) => {
        return count <= Math.min(dailyRemaining, monthlyRemaining, usage_limits.max_urls_per_batch);
      }
    };
  }, [authState.user, usageStats]);

  // Two-Factor Authentication stubs (to be implemented later)
  const isTwoFactorEnabled = useCallback(async (userId: string) => {
    // TODO: Implement with proper 2FA service
    return false;
  }, []);

  const setupTwoFactor = useCallback(async (userId: string, email: string) => {
    // TODO: Implement with proper 2FA service
    return { setup: null, error: 'Two-factor authentication setup will be available soon' };
  }, []);

  const enableTwoFactor = useCallback(async (userId: string, token: string) => {
    // TODO: Implement with proper 2FA service
    return { success: false, error: 'Two-factor authentication will be available soon' };
  }, []);

  const verifyTwoFactor = useCallback(async (userId: string, token: string, backupCode?: string) => {
    // TODO: Implement with proper 2FA service
    return { success: false, error: 'Two-factor authentication will be available soon' };
  }, []);

  const disableTwoFactor = useCallback(async (userId: string, token: string) => {
    // TODO: Implement with proper 2FA service
    return { success: false, error: 'Two-factor authentication will be available soon' };
  }, []);

  const contextValue: AuthContextType = {
    ...authState,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
    refreshUser,
    isAuthenticated,
    isAdmin,
    isPremium,
    canPerformAction,
    getRemainingUsage,
    isTwoFactorEnabled,
    setupTwoFactor,
    enableTwoFactor,
    verifyTwoFactor,
    disableTwoFactor
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

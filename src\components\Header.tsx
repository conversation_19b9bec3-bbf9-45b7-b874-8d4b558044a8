import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import MobileSearch from './MobileSearch';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Search,
  Menu,
  Target,
  BarChart3,
  Bell,
  User,
  Settings,
  LogOut,
  Globe,
  TrendingUp,
  Mail,
  Share2,
  Users,
  MessageSquare,
  Database,
  ChevronDown,
  Zap,
  Shield
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface HeaderProps {
  onMenuClick: () => void;
}

const Header: React.FC<HeaderProps> = ({ onMenuClick }) => {
  const [searchFocused, setSearchFocused] = useState(false);
  const [mobileSearchOpen, setMobileSearchOpen] = useState(false);

  const toolsMenuItems = [
    {
      title: "Web Intelligence",
      items: [
        { icon: Globe, label: "Web Scraper", href: "/web-scraper", description: "Extract data from websites" },
        { icon: Search, label: "SEO Tools", href: "/seo-tools", description: "Optimize search rankings" },
        { icon: BarChart3, label: "Analytics", href: "/analytics", description: "Data insights & reports" },
      ]
    },
    {
      title: "Marketing Suite",
      items: [
        { icon: TrendingUp, label: "Keyword Research", href: "/keyword-research", description: "Find profitable keywords" },
        { icon: Mail, label: "Email Marketing", href: "/email-marketing", description: "Campaign automation" },
        { icon: Share2, label: "Social Media", href: "/social-media", description: "Social management" },
      ]
    },
    {
      title: "Lead Generation",
      items: [
        { icon: Users, label: "Lead Generation", href: "/lead-generation", description: "Find potential customers" },
        { icon: MessageSquare, label: "Content Ideas", href: "/content-ideas", description: "AI content suggestions" },
        { icon: Database, label: "Data Export", href: "/data-export", description: "Export & integrate data" },
      ]
    }
  ];

  return (
    <header className="sticky top-0 z-50 w-full border-b border-white/10 bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 text-white shadow-xl backdrop-blur-sm">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Left Section - Logo & Navigation */}
          <div className="flex items-center space-x-6">
            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onMenuClick}
              className="text-white hover:bg-white/20 lg:hidden"
            >
              <Menu className="h-5 w-5" />
            </Button>

            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Target className="h-8 w-8" />
                <div className="absolute -top-1 -right-1 h-3 w-3 bg-yellow-400 rounded-full animate-pulse" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                  MarketCrawler Pro
                </h1>
                <p className="text-xs text-blue-100 opacity-80">Web Intelligence Suite</p>
              </div>
            </div>

            {/* Desktop Navigation Menu */}
            <NavigationMenu className="hidden lg:flex">
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="bg-transparent text-white hover:bg-white/10 data-[state=open]:bg-white/10">
                    <Zap className="h-4 w-4 mr-2" />
                    Tools
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid gap-3 p-6 w-[800px] grid-cols-3">
                      {toolsMenuItems.map((section) => (
                        <div key={section.title} className="space-y-3">
                          <h4 className="text-sm font-medium text-gray-900 border-b border-gray-200 pb-2">
                            {section.title}
                          </h4>
                          <div className="space-y-2">
                            {section.items.map((item) => (
                              <NavigationMenuLink
                                key={item.href}
                                href={item.href}
                                className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground group"
                              >
                                <div className="flex items-center space-x-2">
                                  <item.icon className="h-4 w-4 text-purple-600 group-hover:text-purple-700" />
                                  <div className="text-sm font-medium leading-none text-gray-900">
                                    {item.label}
                                  </div>
                                </div>
                                <p className="line-clamp-2 text-xs leading-snug text-muted-foreground">
                                  {item.description}
                                </p>
                              </NavigationMenuLink>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuLink
                    href="/analytics"
                    className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-transparent px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-white/10 focus:bg-white/10 focus:outline-none disabled:pointer-events-none disabled:opacity-50"
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Analytics
                  </NavigationMenuLink>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Right Section - Search, Notifications, User Menu */}
          <div className="flex items-center space-x-4">
            {/* Mobile Search Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMobileSearchOpen(true)}
              className="text-white hover:bg-white/10 md:hidden"
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* Desktop Search Bar */}
            <div className="relative hidden md:block">
              <Search className={cn(
                "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors",
                searchFocused ? "text-purple-600" : "text-gray-400"
              )} />
              <Input
                placeholder="Search URLs, keywords, campaigns..."
                className={cn(
                  "pl-10 w-72 transition-all duration-200",
                  searchFocused
                    ? "bg-white border-purple-300 text-gray-900 placeholder:text-gray-500"
                    : "bg-white/10 border-white/20 text-white placeholder:text-white/70 hover:bg-white/15"
                )}
                onFocus={() => setSearchFocused(true)}
                onBlur={() => setSearchFocused(false)}
              />
            </div>

            {/* Quick Actions */}
            <div className="hidden lg:flex items-center space-x-2">
              <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
                <Shield className="h-4 w-4 mr-2" />
                Security
              </Button>
            </div>

            {/* Notifications */}
            <Button variant="ghost" size="sm" className="relative text-white hover:bg-white/10">
              <Bell className="h-5 w-5" />
              <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-red-500 text-white border-2 border-white">
                3
              </Badge>
            </Button>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
                  <User className="h-5 w-5 mr-2" />
                  <span className="hidden sm:inline">Account</span>
                  <ChevronDown className="h-4 w-4 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <BarChart3 className="mr-2 h-4 w-4" />
                  <span>Usage & Billing</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Mobile Search Sheet */}
      <MobileSearch
        isOpen={mobileSearchOpen}
        onOpenChange={setMobileSearchOpen}
      />
    </header>
  );
};

export default Header;
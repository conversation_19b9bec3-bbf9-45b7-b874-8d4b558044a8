import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import Header from './Header';
import NavigationBreadcrumb from './NavigationBreadcrumb';
import { 
  Globe, 
  Search, 
  BarChart3, 
  TrendingUp,
  Smartphone,
  Monitor,
  Tablet,
  Palette,
  Zap,
  Shield,
  Users,
  Star
} from 'lucide-react';

const HeaderDemo: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [currentView, setCurrentView] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const breadcrumbItems = [
    { label: 'Components', href: '/components', icon: Globe },
    { label: 'Navigation', href: '/components/navigation' },
  ];

  const features = [
    {
      icon: Palette,
      title: 'Modern Design',
      description: 'Beautiful gradient background with glassmorphism effects and smooth animations'
    },
    {
      icon: Smartphone,
      title: 'Mobile Responsive',
      description: 'Optimized for all screen sizes with dedicated mobile search and navigation'
    },
    {
      icon: Zap,
      title: 'Quick Actions',
      description: 'Instant access to tools through organized navigation menus and shortcuts'
    },
    {
      icon: Shield,
      title: 'User Management',
      description: 'Complete user account management with notifications and settings'
    },
    {
      icon: Search,
      title: 'Smart Search',
      description: 'Intelligent search with suggestions, filters, and recent history'
    },
    {
      icon: Users,
      title: 'Team Collaboration',
      description: 'Built for teams with role-based access and shared workspaces'
    }
  ];

  const getViewportClass = () => {
    switch (currentView) {
      case 'mobile':
        return 'max-w-sm mx-auto';
      case 'tablet':
        return 'max-w-2xl mx-auto';
      default:
        return 'w-full';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Demo Header */}
      <div className={getViewportClass()}>
        <div className="border border-gray-300 rounded-lg overflow-hidden shadow-lg bg-white">
          <Header onMenuClick={toggleSidebar} />
        </div>
      </div>

      {/* Demo Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb Demo */}
        <NavigationBreadcrumb
          items={breadcrumbItems}
          currentPage="Header Navigation"
          className="mb-8"
        />

        {/* Main Content */}
        <div className="space-y-8">
          {/* Header Overview */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <Star className="h-6 w-6 text-yellow-500" />
                    <span>Redesigned Header Navigation</span>
                  </CardTitle>
                  <CardDescription>
                    A modern, responsive header with enhanced navigation and user experience
                  </CardDescription>
                </div>
                <Badge variant="success">Live Demo</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Viewport Controls */}
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium text-gray-700">Preview:</span>
                  <div className="flex space-x-2">
                    <Button
                      variant={currentView === 'desktop' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setCurrentView('desktop')}
                    >
                      <Monitor className="h-4 w-4 mr-2" />
                      Desktop
                    </Button>
                    <Button
                      variant={currentView === 'tablet' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setCurrentView('tablet')}
                    >
                      <Tablet className="h-4 w-4 mr-2" />
                      Tablet
                    </Button>
                    <Button
                      variant={currentView === 'mobile' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setCurrentView('mobile')}
                    >
                      <Smartphone className="h-4 w-4 mr-2" />
                      Mobile
                    </Button>
                  </div>
                </div>

                {/* Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {features.map((feature, index) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg text-white">
                          <feature.icon className="h-5 w-5" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{feature.title}</h3>
                          <p className="text-sm text-gray-600 mt-1">{feature.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Technical Details */}
          <Tabs defaultValue="features" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="features">Key Features</TabsTrigger>
              <TabsTrigger value="components">Components</TabsTrigger>
              <TabsTrigger value="responsive">Responsive Design</TabsTrigger>
            </TabsList>

            <TabsContent value="features" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Enhanced Navigation Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 text-sm">
                    <li className="flex items-center space-x-2">
                      <Badge variant="info">New</Badge>
                      <span>Organized dropdown menus with categorized tools</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <Badge variant="info">New</Badge>
                      <span>Smart search with focus states and mobile optimization</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <Badge variant="info">New</Badge>
                      <span>User account dropdown with profile and settings</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <Badge variant="info">New</Badge>
                      <span>Notification center with badge indicators</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <Badge variant="info">New</Badge>
                      <span>Mobile-first search sheet with quick actions</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="components" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>UI Components Used</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <strong>Navigation Menu</strong>
                      <p className="text-gray-600">Radix UI navigation with dropdowns</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <strong>Sheet Component</strong>
                      <p className="text-gray-600">Mobile search overlay</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <strong>Dropdown Menu</strong>
                      <p className="text-gray-600">User account management</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <strong>Badge Component</strong>
                      <p className="text-gray-600">Notification indicators</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <strong>Button Variants</strong>
                      <p className="text-gray-600">Ghost, secondary, outline styles</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <strong>Input Component</strong>
                      <p className="text-gray-600">Search with focus states</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="responsive" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Responsive Breakpoints</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 border-l-4 border-green-500 bg-green-50">
                      <strong className="text-green-800">Mobile (< 768px)</strong>
                      <p className="text-green-700 text-sm mt-1">
                        Hamburger menu, mobile search sheet, simplified navigation
                      </p>
                    </div>
                    <div className="p-4 border-l-4 border-blue-500 bg-blue-50">
                      <strong className="text-blue-800">Tablet (768px - 1024px)</strong>
                      <p className="text-blue-700 text-sm mt-1">
                        Condensed navigation, medium search bar, touch-optimized
                      </p>
                    </div>
                    <div className="p-4 border-l-4 border-purple-500 bg-purple-50">
                      <strong className="text-purple-800">Desktop (> 1024px)</strong>
                      <p className="text-purple-700 text-sm mt-1">
                        Full navigation menu, expanded search, all features visible
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default HeaderDemo;

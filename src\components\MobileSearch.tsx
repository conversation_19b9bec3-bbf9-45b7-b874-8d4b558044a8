import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { 
  Search, 
  X, 
  Clock, 
  TrendingUp,
  Globe,
  BarChart3,
  Target
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileSearchProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

const MobileSearch: React.FC<MobileSearchProps> = ({ isOpen, onOpenChange }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [recentSearches] = useState([
    'competitor analysis',
    'keyword research tools',
    'email marketing automation',
    'social media analytics'
  ]);

  const [quickActions] = useState([
    { icon: Globe, label: 'Web Scraper', href: '/web-scraper', color: 'bg-blue-500' },
    { icon: BarChart3, label: 'Analytics', href: '/analytics', color: 'bg-purple-500' },
    { icon: TrendingUp, label: 'Keywords', href: '/keyword-research', color: 'bg-green-500' },
    { icon: Target, label: 'Leads', href: '/lead-generation', color: 'bg-orange-500' },
  ]);

  useEffect(() => {
    if (isOpen) {
      // Focus search input when sheet opens
      setTimeout(() => {
        const input = document.querySelector('#mobile-search-input') as HTMLInputElement;
        if (input) input.focus();
      }, 100);
    }
  }, [isOpen]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Handle search logic here
      console.log('Searching for:', searchQuery);
      onOpenChange(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent side="top" className="h-full">
        <SheetHeader className="text-left">
          <SheetTitle className="flex items-center justify-between">
            <span>Search & Quick Actions</span>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => onOpenChange(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </SheetTitle>
          <SheetDescription>
            Search across your data, campaigns, and tools
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          {/* Search Form */}
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                id="mobile-search-input"
                placeholder="Search URLs, keywords, campaigns..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 h-12 text-lg"
              />
            </div>
            <Button type="submit" className="w-full h-12 text-lg">
              <Search className="h-5 w-5 mr-2" />
              Search
            </Button>
          </form>

          {/* Quick Actions */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700">Quick Actions</h3>
            <div className="grid grid-cols-2 gap-3">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-16 flex-col space-y-2 hover:bg-gray-50"
                  onClick={() => {
                    // Handle navigation
                    window.location.href = action.href;
                    onOpenChange(false);
                  }}
                >
                  <div className={cn("p-2 rounded-lg text-white", action.color)}>
                    <action.icon className="h-5 w-5" />
                  </div>
                  <span className="text-xs font-medium">{action.label}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Recent Searches */}
          {recentSearches.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-700 flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                Recent Searches
              </h3>
              <div className="space-y-2">
                {recentSearches.map((search, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    className="w-full justify-start h-auto p-3 text-left"
                    onClick={() => {
                      setSearchQuery(search);
                      handleSearch({ preventDefault: () => {} } as React.FormEvent);
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      <Search className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">{search}</span>
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Search Tips */}
          <div className="bg-blue-50 rounded-lg p-4 space-y-2">
            <h4 className="text-sm font-medium text-blue-900">Search Tips</h4>
            <div className="space-y-1 text-xs text-blue-700">
              <p>• Use quotes for exact phrases: "email marketing"</p>
              <p>• Filter by type: url:example.com or keyword:seo</p>
              <p>• Search campaigns: campaign:summer-2024</p>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MobileSearch;
